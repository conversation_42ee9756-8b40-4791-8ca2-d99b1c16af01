# STM32F429 信号处理与测量系统 - 项目使用说明

## 项目概述

本项目基于STM32F429微控制器，实现了一个综合的信号处理与测量系统，主要用于频率测量、相位测量、FFT频谱分析、DA输出控制等功能。

## 硬件平台

- **MCU**: STM32F429IGTx (LQFP176封装)
- **核心频率**: 基于25MHz晶振
- **主要外设**:
  - 3路USART通信
  - DAC双通道输出
  - FMC外部存储器接口
  - AD9959 DDS信号发生器
  - 外部AD采样电路

## 项目目录结构

```
├── Core/                     # STM32 HAL库核心文件
│   ├── Inc/                 # 系统头文件
│   └── Src/                 # 系统源文件
├── MY_APP/                  # 应用层代码
│   ├── scheduler.c/h        # 任务调度器
│   ├── app_pid.c/h         # PID控制应用
│   ├── dac_app.c/h         # DAC应用
│   └── bsp_system.h        # 系统板级支持包
├── MY_Algorithms/           # 算法库
│   ├── my_fft.c/h          # FFT频谱分析
│   ├── my_filter.c/h       # 数字滤波器
│   ├── phase_measure.c/h   # 相位测量
│   └── kalman.c/h          # 卡尔曼滤波
├── MY_Hardware_Drivers/     # 硬件驱动层
│   ├── AD9959.c/h          # DDS信号发生器驱动
│   ├── ad_measure.c/h      # AD测量驱动
│   ├── da_output.c/h       # DA输出驱动
│   ├── freq_measure.c/h    # 频率测量驱动
│   └── key_app.c/h         # 按键应用
├── MY_Communication/        # 通信层
│   ├── my_usart.c/h        # USART通信
│   ├── my_usart_pack.c/h   # 串口数据包处理
│   └── my_hmi.c/h          # 人机界面通信
├── MY_Utilities/            # 工具函数
│   ├── cmd_to_fun.c/h      # 命令解析
│   └── commond_init.h      # 通用初始化
├── MDK-ARM/                 # Keil MDK项目文件
├── script/                  # MATLAB脚本和测试文件
└── test/                    # 测试文件
```

## 主要功能模块

### 1. 任务调度系统 (scheduler.c)
- 基于时间片轮转的任务调度器
- 支持多任务并发执行
- 主要任务包括：
  - `ad_proc`: AD采样处理 (1ms周期)
  - `key_proc`: 按键处理 (10ms周期)
  - 其他可配置任务

### 2. AD9959 DDS信号发生器
- 支持4通道DDS信号生成
- 频率范围：基于25MHz晶振
- 相位控制精度：16位
- 支持线性扫频功能

### 3. 信号测量功能
- **频率测量**: 支持双通道频率测量
- **相位测量**: 精确的相位差测量
- **FFT分析**: 1024点FFT频谱分析
- **THD计算**: 总谐波失真分析

### 4. 滤波与算法
- **数字滤波器**: 自定义FIR/IIR滤波器
- **卡尔曼滤波**: 用于信号降噪
- **PID控制**: 闭环控制算法

### 5. 通信接口
- **USART1/2/3**: 多路串口通信
- **数据包协议**: 结构化数据传输
- **HMI接口**: 人机交互界面

## 编译与下载

### 开发环境要求
- **IDE**: Keil MDK-ARM 5.x 或更高版本
- **编译器**: ARM Compiler 6.x
- **调试器**: ST-Link V2/V3

### 编译步骤
1. 打开 `MDK-ARM/zuolan_STM32.uvprojx` 项目文件
2. 选择目标配置 (Debug/Release)
3. 编译项目 (F7)
4. 下载到目标板 (F8)

### 项目配置
- 项目使用STM32CubeMX配置 (`zuolan_STM32.ioc`)
- 主要配置：
  - 系统时钟：最高180MHz
  - USART波特率：115200
  - DAC分辨率：12位
  - FMC时序配置

## 使用说明

### 1. 系统初始化
系统启动后会自动执行以下初始化：
```c
HAL_Init();                    // HAL库初始化
SystemClock_Config();          // 系统时钟配置
外设初始化();                   // GPIO、USART、DAC等
CTRL_INIT();                   // 控制系统初始化
```

### 2. 任务调度
系统采用协作式任务调度，在main函数中循环执行：
```c
while (1) {
    scheduler_run();           // 执行任务调度
}
```

### 3. 串口通信协议
- **波特率**: 115200
- **数据格式**: 8N1
- **通信协议**: 自定义数据包格式
- **输出格式**: `电压值,输出值\r\n`

### 4. 按键操作
- 支持多按键检测
- 按键消抖处理
- 按键事件回调机制

#### 按键功能说明
系统支持4个按键，具体功能如下：

| 按键 | 引脚 | 功能描述 |
|------|------|----------|
| 按键1 | PD6 | 切换DA波形输出 |
| 按键2 | PB4 | 输出当前缓冲区采集波形 |
| 按键3 | PB6 | 调节DA1相位 |
| 按键4 | PB9 | 输出FFT频谱分析结果 |

#### 按键详细功能

**按键1 (PD6) - DA波形切换**
- 循环切换DA输出波形类型
- 支持的波形：正弦波 → 方波 → 三角波 → 锯齿波 → 正弦波
- 串口输出当前波形状态
- 同时更新两个DA通道的波形配置

**按键2 (PB4) - 波形数据输出**
- 通过串口输出当前AD采集缓冲区数据
- 输出格式：每行一个浮点数值
- 数据长度：FIFO_SIZE个采样点
- 用于波形数据分析和调试

**按键3 (PB6) - 相位调节**
- 调节DA1通道的相位偏移
- 每次按键增加30度相位
- 相位范围：0-359度，超过360度自动归零
- 串口输出当前相位值
- 实时应用到硬件输出

**按键4 (PB9) - FFT频谱分析**
- 对当前AD采集数据进行FFT计算
- 输出1024点FFT频谱结果
- 串口输出频谱分析数据
- 可用于信号频域分析

#### 按键使用方法
1. **硬件连接**：确保按键连接到对应GPIO引脚，按键按下时引脚电平为低
2. **软件配置**：按键检测在`key_proc()`函数中，10ms周期调用
3. **调试信息**：所有按键操作都会通过USART1输出调试信息
4. **消抖处理**：系统自动处理按键抖动，确保按键响应稳定

## 测试与验证

### 1. 硬件测试
- 运行 `test/` 目录下的测试程序
- 验证各功能模块工作状态

#### 按键测试步骤
1. **基本按键测试**
   - 依次按下按键1-4，观察串口输出
   - 确认每个按键都有对应的响应信息

2. **DA波形切换测试**（按键1）
   - 按下按键1，观察串口输出波形切换信息
   - 使用示波器观察DA输出波形变化
   - 验证正弦波、方波、三角波、锯齿波切换

3. **波形数据输出测试**（按键2）
   - 按下按键2，观察串口输出的采集数据
   - 数据应为浮点数格式，每行一个数值
   - 可将数据导入MATLAB进行波形分析

4. **相位调节测试**（按键3）
   - 按下按键3，观察串口输出的相位值
   - 使用示波器观察DA1输出相位变化
   - 验证相位以30度步进递增

5. **FFT频谱测试**（按键4）
   - 输入已知频率的测试信号
   - 按下按键4，观察FFT频谱分析结果
   - 验证频谱峰值与输入信号频率一致

#### 测试注意事项
- 确保串口助手波特率设置为115200
- 按键操作间隔应大于10ms（按键扫描周期）
- 测试期间保持系统稳定供电

### 2. MATLAB脚本
- `script/show.m`: 数据可视化
- `script/test_fir.m`: FIR滤波器测试
- `script/test.m`: 综合测试

### 3. 性能指标
- FFT计算时间：< 10ms (1024点)
- 频率测量精度：±0.1Hz
- 相位测量精度：±0.1°

## 注意事项

1. **电源要求**: 确保3.3V电源稳定供电
2. **晶振精度**: 25MHz晶振影响DDS频率精度
3. **EMI防护**: 注意高频信号的EMI影响
4. **调试接口**: 保持SWD接口连接用于调试

## 扩展功能

### 可选功能模块
- 网络通信接口
- 存储器扩展
- 显示屏接口
- 更多算法模块

### 自定义配置
- 修改 `bsp_system.h` 配置系统参数
- 调整 `scheduler.c` 中的任务配置
- 根据需求添加新的驱动模块

## 技术支持

- 查看代码注释了解详细实现
- 参考STM32F429数据手册
- 利用串口调试信息进行问题定位

---

**版本信息**: v1.0  
**更新日期**: 2024年  
**维护者**: 项目开发团队