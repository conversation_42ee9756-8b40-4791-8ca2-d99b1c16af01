# STM32 信号处理与测量系统使用说明

## 项目概述

本项目是一个基于STM32F429的高精度信号处理与测量系统，集成了FFT频谱分析、波形识别、频率测量、相位测量、DAC信号输出等功能。系统采用模块化设计，具有高精度、实时性强的特点。

## 系统架构

### 硬件平台
- **主控芯片**: STM32F429IGT6
- **开发环境**: Keil MDK-ARM
- **数学库**: ARM CMSIS DSP Library
- **外设**: ADC、DAC、UART、GPIO、FMC等

### 软件架构
```
STM32项目
├── Core/                    # STM32 HAL库核心文件
├── Drivers/                 # STM32 HAL驱动和CMSIS库
├── MY_APP/                  # 应用层模块
├── MY_Algorithms/           # 算法模块
├── MY_Communication/        # 通信模块
├── MY_Hardware_Drivers/     # 硬件驱动模块
├── MY_Utilities/           # 工具模块
└── Middlewares/            # 中间件
```

## 主要功能模块

### 1. FFT频谱分析模块 (`MY_Algorithms/my_fft.c`)

#### 核心功能
- **FFT计算**: 1024点FFT变换，支持实时频谱分析
- **频率插值算法**: 
  - 抛物线插值 (`interpolate_peak_frequency`)
  - 质心插值 (`interpolate_peak_frequency_centroid`) - 默认算法，精度更高
- **波形识别**: 自动识别正弦波、方波、三角波、锯齿波
- **频谱特征提取**: 计算频谱质心、带宽、平坦度等参数

#### 主要API
```c
// 执行FFT分析
int perform_fft(float *input_data, float *output_magnitude, uint16_t fft_length);

// 完整波形分析（详细输出）
int analyze_waveform(float *input_data, uint16_t data_length);

// 简化波形分析（仅输出结果）
int analyze_waveform_simple(float *input_data, uint16_t data_length, float *detected_freq);

// 设置采样频率
void set_sample_frequency(float sample_freq);

// 质心插值频率计算
float interpolate_peak_frequency_centroid(float *fft_data, int peak_index, 
                                        uint16_t fft_length, float sample_freq, int window_size);
```

#### 使用示例
```c
// 设置采样频率
set_sample_frequency(2000000.0f);  // 2MHz采样

// 简化分析（推荐用于实时应用）
float detected_freq;
int waveform_type = analyze_waveform_simple(fifo_data1_f, FIFO_SIZE, &detected_freq);

// 波形类型: 0=未知, 1=正弦波, 2=方波, 3=三角波, 4=锯齿波
const char* waveform_names[] = {"Unknown", "Sine", "Square", "Triangle", "Sawtooth"};
printf("检测到: %s波, 频率: %.2f Hz\n", waveform_names[waveform_type], detected_freq);
```

### 2. AD采样模块 (`MY_Hardware_Drivers/ad_measure.c`)

#### 核心功能
- **双通道同步采样**: 支持AD1和AD2同时采样
- **可变采样频率**: 支持60kHz以下正常采样，60kHz以上欠采样
- **实时数据处理**: 自动计算幅值、转换为浮点数
- **FIFO缓冲**: 1024点数据缓冲

#### 主要API
```c
// 设置采样频率
void setSamplingFrequency(float fre, int channel);

// 双通道并行采样
void vpp_adc_parallel(float ad1_freq, float ad2_freq);

// 读取FIFO数据
void readFIFOData(int channel, u16 *fifo_data, float *fifo_data_f);
```

#### 全局变量
```c
extern u16 fifo_data1[FIFO_SIZE], fifo_data2[FIFO_SIZE];       // 原始采样数据
extern float fifo_data1_f[FIFO_SIZE], fifo_data2_f[FIFO_SIZE]; // 浮点数据
extern float vol_amp1, vol_amp2;                               // 计算得到的幅值
```

### 3. 频率测量模块 (`MY_Hardware_Drivers/freq_measure.c`)

#### 核心功能
- **硬件频率计数**: 基于150MHz基准时钟的高精度频率测量
- **双通道测量**: 支持AD1和AD2通道独立频率测量
- **自动校准**: 基准频率自动校准功能

#### 主要API
```c
// 通用频率测量函数
void fre_measure(int channel, u32 *freq_ad, u32 *freq_base, float *freq_result);

// AD1频率测量
void fre_measure_ad1(void);

// AD2频率测量
void fre_measure_ad2(void);
```

### 4. DAC输出模块 (`MY_Hardware_Drivers/da_output.c`)

#### 核心功能
- **双通道DAC输出**: DA1和DA2独立控制
- **频率设置**: 支持任意频率输出
- **幅度控制**: 可调输出幅度
- **相位控制**: 支持相位差设置

#### 主要API
```c
// 设置输出频率
void DA1_OUT(float freq);
void DA2_OUT(float freq);

// 设置输出幅度
void DA_AMPLITUDE(uint16_t vpp1, uint16_t vpp2);

// 设置相位差
void DA_PHASE(uint16_t angle1, uint16_t angle2);
```

### 5. 按键控制模块 (`MY_Hardware_Drivers/key_app.c`)

#### 按键功能定义
- **按键1**: 显示当前AD采集频率
- **按键2**: 执行波形分析并显示结果（简化输出）
- **按键3**: 显示当前波形幅值
- **按键4**: 输出当前缓冲区详细数据

#### 主要API
```c
// 按键处理主函数
void key_proc(void);

// 设置/获取当前AD采集频率
void set_current_ad_frequency(float freq);
float get_current_ad_frequency(void);
```

### 6. 任务调度模块 (`MY_APP/scheduler.c`)

#### 核心功能
- **时间片轮转调度**: 基于HAL_GetTick()的任务调度
- **可配置任务频率**: 每个任务可独立设置执行频率
- **模块化任务管理**: 易于添加和删除任务

#### 当前任务配置
```c
static task_t scheduler_task[] = {
    {ad_proc, 1, 0},      // AD采集任务，每1ms执行
    {key_proc, 10, 0},    // 按键处理任务，每10ms执行
};
```

## 使用指南

### 1. 系统初始化

```c
#include "bsp_system.h"

int main(void) {
    // HAL库初始化
    HAL_Init();
    SystemClock_Config();
    
    // 外设初始化
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    MX_DAC_Init();
    MX_FMC_Init();
    
    // 调度器初始化
    scheduler_init();
    
    // 主循环
    while(1) {
        scheduler_run();
    }
}
```

### 2. 基本测量流程

#### 步骤1: 设置采样参数
```c
// 设置AD采集频率为2MHz
set_current_ad_frequency(2000000.0f);

// 设置FFT分析的采样频率
set_sample_frequency(2000000.0f);
```

#### 步骤2: 执行测量
```c
// 自动采样（由调度器每1ms调用）
ad_proc();  // 执行AD采样

// 手动触发波形分析（按键2或直接调用）
float detected_freq;
int waveform_type = analyze_waveform_simple(fifo_data1_f, FIFO_SIZE, &detected_freq);
```

#### 步骤3: 结果输出
```c
// 输出到DAC
DA1_OUT(detected_freq);
DA2_OUT(detected_freq);

// 串口输出结果
my_printf(&huart1, "波形类型: %s, 频率: %.2f Hz\n", 
          waveform_names[waveform_type], detected_freq);
```

### 3. 高级功能使用

#### FFT调试输出控制
```c
// 启用详细FFT调试输出（在my_fft.h中取消注释）
#define FFT_DEBUG_OUTPUT

// 使用详细分析函数
int waveform_type = analyze_waveform(fifo_data1_f, FIFO_SIZE);
```

#### 自定义频率插值
```c
// 使用质心插值（推荐）
float precise_freq = interpolate_peak_frequency_centroid(
    fft_magnitude, peak_index, FFT_LENGTH, sample_freq, 5);

// 使用抛物线插值
float precise_freq = interpolate_peak_frequency(
    fft_magnitude, peak_index, FFT_LENGTH, sample_freq);
```

## 性能参数

### FFT分析性能
- **FFT点数**: 1024点
- **频率分辨率**: 采样频率/1024
- **处理时间**: 约10ms（STM32F429@180MHz）
- **频率精度**: 质心插值可提升56%精度

### 采样性能
- **最大采样频率**: 2MHz（双通道同步）
- **采样精度**: 12位ADC
- **缓冲区大小**: 1024点
- **电压范围**: ±10V

### 输出性能
- **DAC分辨率**: 12位
- **输出频率范围**: DC - 数MHz
- **相位精度**: 约0.35°（360°/1024）

## 注意事项

### 1. 采样频率设置
- 对于60kHz以下信号，使用正常采样
- 对于60kHz以上信号，系统自动切换到欠采样模式
- 确保采样频率至少是信号频率的2倍（奈奎斯特定理）

### 2. FFT分析限制
- 输入信号应尽量避免直流分量
- 对于非周期信号，可能出现频谱泄漏
- 建议使用窗函数减少频谱泄漏（当前未实现）

### 3. 内存使用
- FFT计算需要较大内存空间
- 确保系统有足够的RAM（推荐256KB以上）

### 4. 实时性考虑
- FFT计算较为耗时，不建议在中断中调用
- 使用`analyze_waveform_simple`获得更好的实时性

## 故障排除

### 常见问题

1. **FFT结果不准确**
   - 检查采样频率设置是否正确
   - 确认输入信号幅度在合理范围内
   - 验证信号是否稳定

2. **按键无响应**
   - 检查GPIO配置
   - 确认按键去抖动时间
   - 验证调度器是否正常运行

3. **DAC输出异常**
   - 检查DAC初始化
   - 确认频率控制字计算
   - 验证输出负载

4. **串口输出乱码**
   - 检查波特率设置
   - 确认UART配置
   - 验证printf重定向

## 扩展开发

### 添加新的波形类型
1. 在`classify_waveform`函数中添加新的识别算法
2. 更新`waveform_names`数组
3. 调整评分阈值

### 添加新的滤波算法
1. 在`MY_Algorithms`目录下添加新的算法文件
2. 在`my_filter.c`中实现滤波函数
3. 在FFT分析前调用滤波函数

### 添加新的通信接口
1. 在`MY_Communication`目录下添加新的通信模块
2. 实现相应的协议解析
3. 在调度器中添加通信任务

## 版本信息

- **当前版本**: v1.0
- **最后更新**: 2025年7月
- **兼容性**: STM32F4系列
- **依赖库**: ARM CMSIS DSP Library v1.9.0+

## 技术支持

如有技术问题，请检查：
1. 硬件连接是否正确
2. 软件配置是否匹配
3. 参考本文档的故障排除部分
4. 查看代码注释获取更多细节

---

*本文档基于项目当前状态编写，如有更新请及时同步文档内容。*