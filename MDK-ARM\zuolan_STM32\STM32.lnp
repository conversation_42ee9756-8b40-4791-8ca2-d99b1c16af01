--cpu=Cortex-M4.fp.sp
".\zuolan_stm32\startup_stm32f429xx.o"
".\zuolan_stm32\main.o"
".\zuolan_stm32\gpio.o"
".\zuolan_stm32\dac.o"
".\zuolan_stm32\fmc.o"
".\zuolan_stm32\usart.o"
".\zuolan_stm32\stm32f4xx_it.o"
".\zuolan_stm32\stm32f4xx_hal_msp.o"
".\zuolan_stm32\ad_measure.o"
".\zuolan_stm32\ad9959.o"
".\zuolan_stm32\da_output.o"
".\zuolan_stm32\freq_measure.o"
".\zuolan_stm32\key_app.o"
".\zuolan_stm32\my_fft.o"
".\zuolan_stm32\my_filter.o"
".\zuolan_stm32\phase_measure.o"
".\zuolan_stm32\kalman.o"
".\zuolan_stm32\my_hmi.o"
".\zuolan_stm32\my_usart.o"
".\zuolan_stm32\my_usart_pack.o"
".\zuolan_stm32\cmd_to_fun.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4lf_math.lib"
".\zuolan_stm32\scheduler.o"
".\zuolan_stm32\app_pid.o"
".\zuolan_stm32\stm32f4xx_hal_dac.o"
".\zuolan_stm32\stm32f4xx_hal_dac_ex.o"
".\zuolan_stm32\stm32f4xx_hal_rcc.o"
".\zuolan_stm32\stm32f4xx_hal_rcc_ex.o"
".\zuolan_stm32\stm32f4xx_hal_flash.o"
".\zuolan_stm32\stm32f4xx_hal_flash_ex.o"
".\zuolan_stm32\stm32f4xx_hal_flash_ramfunc.o"
".\zuolan_stm32\stm32f4xx_hal_gpio.o"
".\zuolan_stm32\stm32f4xx_hal_dma_ex.o"
".\zuolan_stm32\stm32f4xx_hal_dma.o"
".\zuolan_stm32\stm32f4xx_hal_pwr.o"
".\zuolan_stm32\stm32f4xx_hal_pwr_ex.o"
".\zuolan_stm32\stm32f4xx_hal_cortex.o"
".\zuolan_stm32\stm32f4xx_hal.o"
".\zuolan_stm32\stm32f4xx_hal_exti.o"
".\zuolan_stm32\stm32f4xx_ll_fmc.o"
".\zuolan_stm32\stm32f4xx_hal_sram.o"
".\zuolan_stm32\stm32f4xx_hal_tim.o"
".\zuolan_stm32\stm32f4xx_hal_tim_ex.o"
".\zuolan_stm32\stm32f4xx_hal_uart.o"
".\zuolan_stm32\system_stm32f4xx.o"
--library_type=microlib --strict --scatter ".\zuolan_STM32\STM32.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32.map" -o .\zuolan_STM32\STM32.axf